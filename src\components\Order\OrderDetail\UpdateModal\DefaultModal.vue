<script setup lang="ts">
import { computed, ref } from 'vue';
import { TransitionRoot, TransitionChild, Dialog, DialogPanel, DialogTitle } from '@headlessui/vue';

const props = defineProps<{
  title: string;
  showCloseButton: boolean;
  clickOutsideClose: boolean;
  modalWidth?: string;
}>();
const emits = defineEmits(['closeModal']);

const isOpen = ref(false);

const handleClose = () => {
  emits('closeModal');
};

const closeModal = () => {
  isOpen.value = false;
};

const clickOutsideclose = () => {
  if (props.clickOutsideClose) {
    closeModal();
  }
};

const openModal = () => {
  console.log('DefaultModal openModal 被調用');
  console.log('設置 isOpen 為 true');
  isOpen.value = true;
  console.log('isOpen.value 現在是:', isOpen.value);
};

const computedWidth = computed(() => {
  return props.modalWidth ? props.modalWidth : 'max-w-3xl';
});

defineExpose({ openModal, closeModal });
</script>

<template>
  <TransitionRoot as="template" @close="clickOutsideclose" :show="isOpen">
    <Dialog as="div" static class="relative z-[60]">
      <TransitionChild as="template" enter="ease-out duration-300" enter-from="opacity-0" enter-to="opacity-100"
                       leave="ease-in duration-200" leave-from="opacity-100" leave-to="opacity-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
      </TransitionChild>

      <div class="fixed inset-0 z-10 overflow-y-auto text-ch">
        <div class="flex min-h-full justify-center p-4 text-center items-center md:p-0">
          <TransitionChild as="template" enter="ease-out duration-300"
                           enter-from="opacity-0 translate-y-4 md:translate-y-0 md:scale-95"
                           enter-to="opacity-100 translate-y-0 md:scale-100" leave="ease-in duration-200"
                           leave-from="opacity-100 translate-y-0 md:scale-100"
                           leave-to="opacity-0 translate-y-4 md:translate-y-0 md:scale-95">
            <DialogPanel
              :class="[computedWidth,'relative transform overflow-hidden rounded-lg bg-white pb-4 text-left shadow-xl transition-all md:my-8 w-full']">
              <div>
                <div class="text-center">
                  <DialogTitle as="div"
                               class="flex-center flex text-xl font-bold leading-6 py-4 px-4 hez-item-bg-color">
                    <p class="flex-grow">{{ props.title }}</p>
                    <button @click="handleClose" v-if="showCloseButton" class="absolute right-4">
                      <img src="/vectors/general/close.svg" alt="star" class="ml-auto w-7 h-7" />
                    </button>
                  </DialogTitle>
                  <slot />
                </div>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>
